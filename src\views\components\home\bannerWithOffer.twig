{#
| Variable                | Type     | Description                                                                  |
|-------------------------|----------|------------------------------------------------------------------------------|
| component               | object   | Contains merchant settings for fields from twilight.json `component` section |
| component.banner_image  | string   | Banner image URL                                                             |
| component.offer_link    | object   | Offer link (variable-list format)                                           |
| component.offer_title   | string   | Offer title text                                                             |
| component.show_countdown| boolean  | Show/hide countdown timer                                                    |
| component.timer_days    | number   | Countdown days                                                               |
| component.timer_hours   | number   | Countdown hours                                                              |
| component.timer_minutes | number   | Countdown minutes                                                            |
| component.countdown_bg_color | string | Countdown background color                                                |
| component.countdown_text_color | string | Countdown text color                                                    |
| component.title_color   | string   | Title text color                                                             |
| component.animation_enabled | boolean | Enable/disable animations                                                |
| component.animation_type | string  | Animation type (fade-in, slide-up, scale-in)                                |
| component.animation_speed | string | Animation speed (slow, normal, fast)                                        |
| position                | int      | Component position for unique IDs                                           |
#}

{# Extract component data with fallbacks #}
{% set banner_image = component.banner_image|default('') %}
{% set unique_id = 'banner-offer-' ~ position %}

{# Extract text settings #}
{% set offer_title = component.offer_title|default('عرض خاص') %}
{% set show_countdown = component.show_countdown|default(true) %}

{# Extract countdown settings #}
{% set timer_days = component.timer_days|default(7) %}
{% set timer_hours = component.timer_hours|default(12) %}
{% set timer_minutes = component.timer_minutes|default(30) %}

{# Extract color settings #}
{% set countdown_bg_color = component.countdown_bg_color|default('#00bfe0') %}
{% set countdown_text_color = component.countdown_text_color|default('#ffffff') %}
{% set title_color = component.title_color|default('#ffffff') %}

{# Extract animation settings #}
{% set animation_enabled = component.animation_enabled|default(true) %}
{% set animation_type = component.animation_type|default('fade-in') %}
{% set animation_speed = component.animation_speed|default('normal') %}

{# Extract URL from variable-list format #}
{% set offer_url = null %}
{% if component.offer_link is defined and component.offer_link %}
    {% if component.offer_link.url is defined %}
        {% set offer_url = component.offer_link.url %}
    {% elseif component.offer_link is iterable and component.offer_link|length > 0 %}
        {% set offer_url = component.offer_link[0].url|default(null) %}
    {% endif %}
{% endif %}

{# Component rendering #}
{% if banner_image %}
    <section class="s-block s-block--banner-with-offer{% if animation_enabled %} animate-{{ animation_type }} animate-{{ animation_speed }}{% endif %}"
             id="{{ unique_id }}"
             data-timer-days="{{ timer_days }}"
             data-timer-hours="{{ timer_hours }}"
             data-timer-minutes="{{ timer_minutes }}">
        <div class="banner-offer-wrapper">
            <div class="offer-container">
                {% if offer_url and offer_url != '' and offer_url != '#' %}
                    <a href="{{ offer_url }}"
                       class="offer-link"
                       aria-label="رابط العرض"
                       rel="noopener noreferrer">
                        <img src="{{ banner_image }}"
                             alt="بنر العرض"
                             class="banner-image"
                             loading="lazy"
                             decoding="async"
                             onerror="this.style.display='none'; this.parentElement.classList.add('banner-error');"
                             onload="this.style.opacity='1'; this.classList.add('banner-loaded');">

                        <div class="offer-content">
                            <h2 class="offer-title" style="color: {{ title_color }};">{{ offer_title }}</h2>
                            {% if show_countdown %}
                                <div class="countdown-timer">
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-days>{{ '%02d'|format(timer_days) }}</span>
                                        <span class="countdown-label">يوم</span>
                                    </div>
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-hours>{{ '%02d'|format(timer_hours) }}</span>
                                        <span class="countdown-label">ساعة</span>
                                    </div>
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-minutes>{{ '%02d'|format(timer_minutes) }}</span>
                                        <span class="countdown-label">دقيقة</span>
                                    </div>
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-seconds>00</span>
                                        <span class="countdown-label">ثانية</span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <div class="overlay"></div>
                    </a>
                {% else %}
                    <div class="offer-container-no-link">
                        <img src="{{ banner_image }}"
                             alt="بنر العرض"
                             class="banner-image"
                             loading="lazy"
                             decoding="async"
                             onerror="this.style.display='none'; this.parentElement.classList.add('banner-error');"
                             onload="this.style.opacity='1'; this.classList.add('banner-loaded');">

                        <div class="offer-content">
                            <h2 class="offer-title" style="color: {{ title_color }};">{{ offer_title }}</h2>
                            {% if show_countdown %}
                                <div class="countdown-timer">
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-days>{{ '%02d'|format(timer_days) }}</span>
                                        <span class="countdown-label">يوم</span>
                                    </div>
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-hours>{{ '%02d'|format(timer_hours) }}</span>
                                        <span class="countdown-label">ساعة</span>
                                    </div>
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-minutes>{{ '%02d'|format(timer_minutes) }}</span>
                                        <span class="countdown-label">دقيقة</span>
                                    </div>
                                    <div class="countdown-box" style="background-color: {{ countdown_bg_color }}; color: {{ countdown_text_color }};">
                                        <span class="countdown-number" data-seconds>00</span>
                                        <span class="countdown-label">ثانية</span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>

                        <div class="overlay"></div>
                    </div>
                {% endif %}
            </div>
        </div>
    </section>
{% else %}
    {# Placeholder when no image is provided #}
    <section class="s-block s-block--banner-with-offer" id="{{ unique_id }}">
        <div class="banner-offer-wrapper">
            <div class="offer-container">
                <div class="placeholder-banner">
                    <div class="placeholder-content">
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            height: 400px;
                            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
                            color: #1de9b6;
                            font-size: 16px;
                            text-align: center;
                            border-radius: 12px;
                            border: 2px dashed #1de9b6;
                        ">
                            <div>
                                <div style="font-size: 48px; margin-bottom: 10px;">🎯</div>
                                <div style="font-weight: bold; margin-bottom: 5px;">بنر مع عرض</div>
                                <div style="font-size: 14px; opacity: 0.7;">قم بإضافة صورة البنر من لوحة التحكم</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
{% endif %}

{# Inline JavaScript for immediate functionality #}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Banner with Offer Animation Manager
    class BannerWithOfferManager {
        constructor() {
            this.observer = null;
            this.animatedElements = new Set();
            this.init();
        }

        init() {
            this.createObserver();
            this.observeBannerSections();
            this.initCountdown();
        }

        createObserver() {
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.animatedElements.has(entry.target)) {
                        this.animateElement(entry.target);
                        this.animatedElements.add(entry.target);
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.15,
                rootMargin: '50px 0px'
            });
        }

        observeBannerSections() {
            const bannerSections = document.querySelectorAll('.s-block--banner-with-offer');
            bannerSections.forEach(section => {
                this.observer.observe(section);
            });
        }

        animateElement(element) {
            const wrapper = element.querySelector('.banner-offer-wrapper');
            const content = element.querySelector('.offer-content');
            const title = element.querySelector('.offer-title');
            const countdown = element.querySelector('.countdown-timer');

            if (wrapper) {
                wrapper.classList.add('animate-in');
            }

            setTimeout(() => {
                if (content) content.classList.add('animate-in');
            }, 200);

            setTimeout(() => {
                if (title) title.classList.add('animate-in');
            }, 400);

            setTimeout(() => {
                if (countdown) countdown.classList.add('animate-in');
            }, 600);
        }

        initCountdown() {
            // Simple countdown timer (can be customized)
            const countdownElements = document.querySelectorAll('.countdown-timer');
            countdownElements.forEach(timer => {
                this.startCountdown(timer);
            });
        }

        startCountdown(timer) {
            // Set a default countdown of 7 days from now
            const endDate = new Date();
            endDate.setDate(endDate.getDate() + 7);

            const updateCountdown = () => {
                const now = new Date().getTime();
                const distance = endDate.getTime() - now;

                if (distance < 0) {
                    timer.innerHTML = '<div class="countdown-expired">انتهى العرض</div>';
                    return;
                }

                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);

                const daysEl = timer.querySelector('[data-days]');
                const hoursEl = timer.querySelector('[data-hours]');
                const minutesEl = timer.querySelector('[data-minutes]');
                const secondsEl = timer.querySelector('[data-seconds]');

                if (daysEl) daysEl.textContent = String(days).padStart(2, '0');
                if (hoursEl) hoursEl.textContent = String(hours).padStart(2, '0');
                if (minutesEl) minutesEl.textContent = String(minutes).padStart(2, '0');
                if (secondsEl) secondsEl.textContent = String(seconds).padStart(2, '0');
            };

            updateCountdown();
            setInterval(updateCountdown, 1000);
        }

        destroy() {
            if (this.observer) {
                this.observer.disconnect();
            }
            this.animatedElements.clear();
        }
    }

    // Initialize the manager
    const bannerManager = new BannerWithOfferManager();

    // Make it globally accessible for external control
    window.BannerWithOfferManager = bannerManager;
});
</script>