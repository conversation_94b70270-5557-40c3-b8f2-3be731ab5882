/* Gaming Theme Banner Styles - Optimized for Performance */

/* Use transform3d for hardware acceleration */
.gaming-theme-slider {
    margin-bottom: 2rem;
    will-change: transform; /* Hint browser for optimization */
}

/* Optimized pop-up animation */
.gaming-banners-section {
    opacity: 0;
    transform: translate3d(0, 20px, 0) scale(0.98);
    transition: opacity 0.6s ease, transform 0.6s ease;
}

.gaming-banners-section.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0) scale(1);
}

.gaming-banner-slide {
    height: 500px;
    position: relative;
    overflow: hidden;
    contain: layout style paint; /* CSS containment for performance */
}

.gaming-banner-image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.gaming-banner-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.8s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-image {
    transform: scale3d(1.05, 1.05, 1);
}

/* Optimized overlay with better performance */
.gaming-banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to top,
        rgba(10, 10, 15, 0.9) 0%,
        rgba(10, 10, 15, 0.6) 40%,
        rgba(10, 10, 15, 0.3) 70%,
        rgba(10, 10, 15, 0.1) 100%);
    z-index: 1;
}

.gaming-banner-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 3rem;
    z-index: 2;
    color: #fff;
    text-align: start;
    transform: translate3d(0, 0, 0);
    transition: transform 0.4s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-content {
    transform: translate3d(0, -10px, 0);
}

/* Optimized text animations */
.animate-element {
    opacity: 0;
    transform: translate3d(0, 30px, 0);
    transition: opacity 0.6s ease, transform 0.6s ease;
    transition-delay: 0s;
}

.animate-element.animate-in {
    opacity: 1;
    transform: translate3d(0, 0, 0);
}

.gaming-banner-title {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.7);
    color: #fff;
    position: relative;
    display: inline-block;
}

/* Optimized pseudo-element animation */
.gaming-banner-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 0;
    height: 3px;
    background: var(--color-primary, #1DE9B6);
    box-shadow: 0 0 10px var(--color-primary, #1DE9B6);
    transition: width 0.6s ease 0.6s;
}

.gaming-banner-title.animate-in::after {
    width: 60px;
}

.gaming-banner-subtitle {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.7);
    margin-bottom: 1.5rem;
    max-width: 600px;
}

.gaming-banner-button {
    display: inline-flex;
    align-items: center;
    background: rgba(29, 233, 182, 0.2);
    border: 1px solid var(--color-primary, #1DE9B6);
    color: #fff;
    padding: 0.75rem 1.5rem;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 0 15px rgba(29, 233, 182, 0.3);
    cursor: pointer;
    will-change: transform, box-shadow;
}

.gaming-banner-button .btn-icon {
    margin-right: 10px;
    margin-left: 10px;
    transition: transform 0.3s ease;
    will-change: transform;
}

.gaming-banner-link:hover .gaming-banner-button {
    background: rgba(29, 233, 182, 0.3);
    box-shadow: 0 0 20px rgba(29, 233, 182, 0.5);
}

.gaming-banner-link:hover .gaming-banner-button .btn-icon {
    transform: translate3d(5px, 0, 0);
}

/* Optimized animated elements */
.gaming-banner-effects {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1;
    pointer-events: none;
    contain: layout style paint;
}

/* Reduced and optimized particles */
.gaming-particle {
    position: absolute;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    border-radius: 50%;
    animation: optimizedFloat 12s infinite ease-in-out;
    animation-delay: var(--delay, 0s);
    opacity: 0;
    will-change: transform, opacity;
}

.particle-1 {
    top: 20%;
    left: 15%;
    width: 100px;
    height: 100px;
}

.particle-2 {
    top: 60%;
    right: 20%;
    width: 80px;
    height: 80px;
}

.gaming-glow {
    position: absolute;
    bottom: -30px;
    left: 0;
    right: 0;
    height: 60px;
    background: radial-gradient(ellipse at center, rgba(29, 233, 182, 0.15) 0%, rgba(29, 233, 182, 0) 70%);
    opacity: 0.5;
}

/* Optimized animations using transform3d */
@keyframes optimizedFloat {
    0% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
    25% {
        opacity: 0.4;
    }
    50% {
        transform: translate3d(10px, -15px, 0);
        opacity: 0.6;
    }
    75% {
        opacity: 0.4;
    }
    100% {
        transform: translate3d(0, 0, 0);
        opacity: 0;
    }
}

/* Responsive optimizations */
@media (max-width: 1024px) {
    .gaming-banner-slide {
        height: 450px;
    }
    
    .gaming-banner-title {
        font-size: 2rem;
    }
    
    .gaming-banner-subtitle {
        font-size: 1.1rem;
    }
    
    /* Reduce particles on smaller screens */
    .gaming-particle {
        width: 60px;
        height: 60px;
    }
}

@media (max-width: 768px) {
    .gaming-banner-slide {
        height: 350px;
    }
    
    .gaming-banner-title {
        font-size: 1.75rem;
    }
    
    .gaming-banner-subtitle {
        font-size: 1rem;
    }
    
    .gaming-banner-content {
        padding: 2rem;
    }
    
    .gaming-banner-button {
        padding: 0.6rem 1.2rem;
    }
    
    /* Disable particles on mobile for better performance */
    .gaming-particle {
        display: none;
    }
}

@media (max-width: 480px) {
    .gaming-banner-slide {
        height: 300px;
    }
    
    .gaming-banner-title {
        font-size: 1.5rem;
    }
    
    .gaming-banner-subtitle {
        font-size: 0.9rem;
        margin-bottom: 1rem;
    }
    
    .gaming-banner-content {
        padding: 1.5rem;
    }
    
    .gaming-banner-button {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    /* Disable glow effects on very small screens */
    .gaming-glow {
        display: none;
    }
}

/* Prefers reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .gaming-particle,
    .gaming-glow {
        animation: none;
        opacity: 0;
    }

    .gaming-banner-image,
    .gaming-banner-content,
    .animate-element {
        transition: none;
    }
}

/* Critical performance optimizations */
.gaming-banners-section {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 500px;
}

/* Optimize for low-end devices */
@media (max-width: 768px) and (max-resolution: 1.5dppx) {
    .gaming-particle,
    .gaming-glow {
        display: none;
    }

    .gaming-banner-image {
        transition: none;
    }

    .gaming-banner-link:hover .gaming-banner-image {
        transform: none;
    }
}

/* Print styles */
@media print {
    .gaming-particle,
    .gaming-glow,
    .gaming-banner-effects {
        display: none;
    }
}
