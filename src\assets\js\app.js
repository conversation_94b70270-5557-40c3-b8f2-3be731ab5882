import MobileMenu from 'mmenu-light';
import Swal from 'sweetalert2';
import Anime from './partials/anime';
import initTootTip from './partials/tooltip';
import AppHelpers from "./app-helpers";
import './utils/performance-optimizer';
import './components/universal-product-cards-animation';
import './components/whatsapp';
import './components/loading-screen';
import './components/special-gallery';
import './components/video-banner';
import './components/special-banners';
import './components/common-questions';
import './components/special-gallery-gaming';
import './components/product-with-icons';
import './partials/categories-dropdown';
import './components/wahg-banner';
import './components/moving-text';
import './components/navbar-logo-settings';
import './components/salla-productlist-animation';
import './components/first-gallery-animation';
import './components/banner-with-offer-animation';
import './components/special-title-animation';
import './components/second-gallery-animation';
import './components/store-categories';
import './components/line-break-enhanced';
import './components/wahg-banner-enhanced';
import './components/first-gallery-enhanced';
import './components/wahg-banner-enhanced';

class App extends AppHelpers {
  constructor() {
    super();
    window.app = this;
  }

  loadTheApp() {
    this.commonThings();
    this.initiateNotifier();
    this.initiateMobileMenu();
    if (header_is_sticky) {
      this.initiateStickyMenu();
    }
    this.initAddToCart();
    this.initiateDropdowns();
    this.initiateSallaUserMenu();
    this.initiateModals();

    // إنشاء السايدبار بعد تحميل الصفحة
    setTimeout(() => {
      console.log('إنشاء السايدبار...');
      this.createUserMenuSidebar();
    }, 1000);
    this.initiateCollapse();
    this.initAttachWishlistListeners();
    this.changeMenuDirection()
    initTootTip();
    this.loadModalImgOnclick();
    this.initBannerWithOffer();

    salla.comment.event.onAdded(() => window.location.reload());

    this.status = 'ready';
    document.dispatchEvent(new CustomEvent('theme::ready'));
    this.log('Theme Loaded 🎉');
  }

  log(message) {
    salla.log(`ThemeApp(Raed)::${message}`);
    return this;
  }

    // fix Menu Direction at the third level >> The menu at the third level was popping off the page
    changeMenuDirection(){
      app.all('.root-level.has-children',item=>{
        if(item.classList.contains('change-menu-dir')) return;
        app.on('mouseover',item,()=>{
          let submenu = item.querySelector('.sub-menu .sub-menu');
          if(submenu){
            let rect = submenu.getBoundingClientRect();
            (rect.left < 10 || rect.right > window.innerWidth - 10) && app.addClass(item,'change-menu-dir')
          }      
        })
      })
    }

  loadModalImgOnclick(){
    document.querySelectorAll('.load-img-onclick').forEach(link => {
      link.addEventListener('click', (event) => {
        event.preventDefault();
        let modal = document.querySelector('#' + link.dataset.modalId),
          img = modal.querySelector('img'),
          imgSrc = img.dataset.src;
        modal.open();

        if (img.classList.contains('loaded')) return;

        img.src = imgSrc;
        img.classList.add('loaded');
      })
    })
  }

  commonThings() {
    this.cleanContentArticles('.content-entry');
  }

  cleanContentArticles(elementsSelector) {
    let articleElements = document.querySelectorAll(elementsSelector);

    if (articleElements.length) {
      articleElements.forEach(article => {
        article.innerHTML = article.innerHTML.replace(/\&nbsp;/g, ' ')
      })
    }
  }

isElementLoaded(selector){
  return new Promise((resolve=>{
    const interval=setInterval(()=>{
    if(document.querySelector(selector)){
      clearInterval(interval)
      return resolve(document.querySelector(selector))
    }
   },160)
}))

  
  };

  copyToClipboard(event) {
    event.preventDefault();
    let aux = document.createElement("input"),
    btn = event.currentTarget;
    aux.setAttribute("value", btn.dataset.content);
    document.body.appendChild(aux);
    aux.select();
    document.execCommand("copy");
    document.body.removeChild(aux);
    this.toggleElementClassIf(btn, 'copied', 'code-to-copy', () => true);
    setTimeout(() => {
      this.toggleElementClassIf(btn, 'code-to-copy', 'copied', () => true)
    }, 1000);
  }

  initiateNotifier() {
    salla.notify.setNotifier(function (message, type, data) {
      if (typeof message == 'object') {
        return Swal.fire(message).then(type);
      }

      return Swal.mixin({
        toast: true,
        position: salla.config.get('theme.is_rtl') ? 'top-start' : 'top-end',
        showConfirmButton: false,
        timer: 2000,
        didOpen: (toast) => {
          toast.addEventListener('mouseenter', Swal.stopTimer)
          toast.addEventListener('mouseleave', Swal.resumeTimer)
        }
      }).fire({
        icon: type,
        title: message,
        showCloseButton: true,
        timerProgressBar: true
      })
    });
  }


  initiateMobileMenu() {

  this.isElementLoaded('#mobile-menu').then((menu) => {

 
  const mobileMenu = new MobileMenu(menu, "(max-width: 1024px)", "( slidingSubmenus: false)");

  salla.lang.onLoaded(() => {
    mobileMenu.navigation({ title: salla.lang.get('blocks.header.main_menu') });
  });
  const drawer = mobileMenu.offcanvas({ position: salla.config.get('theme.is_rtl') ? "right" : 'left' });

  this.onClick("a[href='#mobile-menu']", event => {
    document.body.classList.add('menu-opened');
    event.preventDefault() || drawer.close() || drawer.open()
    
  });
  this.onClick(".close-mobile-menu", event => {
    document.body.classList.remove('menu-opened');
    event.preventDefault() || drawer.close()
  });
  });

  }
 initAttachWishlistListeners() {
    let isListenerAttached = false;
  
    function toggleFavoriteIcon(id, isAdded = true) {
      document.querySelectorAll('.s-product-card-wishlist-btn[data-id="' + id + '"]').forEach(btn => {
        app.toggleElementClassIf(btn, 's-product-card-wishlist-added', 'not-added', () => isAdded);
        app.toggleElementClassIf(btn, 'pulse-anime', 'un-favorited', () => isAdded);
      });
    }
  
    if (!isListenerAttached) {
      salla.wishlist.event.onAdded((event, id) => toggleFavoriteIcon(id));
      salla.wishlist.event.onRemoved((event, id) => toggleFavoriteIcon(id, false));
      isListenerAttached = true; // Mark the listener as attached
    }
  }

  initiateStickyMenu() {
    let header = this.element('#mainnav'),
      height = this.element('#mainnav .inner')?.clientHeight;
    //when it's landing page, there is no header
    if (!header) {
      return;
    }

    window.addEventListener('load', () => setTimeout(() => this.setHeaderHeight(), 500))
    window.addEventListener('resize', () => this.setHeaderHeight())

    window.addEventListener('scroll', () => {
      const scrollY = window.scrollY;
      const headerOffset = header.offsetTop + height;

      // Original sticky functionality
      if (scrollY >= headerOffset) {
        header.classList.add('fixed-pinned', 'animated');
      } else {
        header.classList.remove('fixed-pinned');
      }

      if (scrollY >= 200) {
        header.classList.add('fixed-header');
      } else {
        header.classList.remove('fixed-header', 'animated');
      }

      // Glass/Blur effect when scrolling
      if (scrollY > 50) {
        header.classList.add('scrolled-glass');
      } else {
        header.classList.remove('scrolled-glass');
      }
    }, { passive: true });
  }

  setHeaderHeight() {
    let height = this.element('#mainnav .inner').clientHeight,
      header = this.element('#mainnav');
    header.style.height = height + 'px';
  }

  initiateDropdowns() {
    this.onClick('.dropdown__trigger', ({ target: btn }) => {
      const isOpening = !btn.parentElement.classList.contains('is-opened');

      btn.parentElement.classList.toggle('is-opened');
      document.body.classList.toggle('dropdown--is-opened');

      // إذا كانت القائمة تفتح، احسب الموقع الصحيح
      if (isOpening) {
        this.positionDropdownMenu(btn);
      }

      // Click Outside || Click on close btn
      window.addEventListener('click', ({ target: element }) => {
        if (!element.closest('.dropdown__menu') && element !== btn || element.classList.contains('dropdown__close')) {
          btn.parentElement.classList.remove('is-opened');
          document.body.classList.remove('dropdown--is-opened');
        }
      });
    });
  }

  positionDropdownMenu(trigger) {
    // لا نحتاج لتعديل الموقع، فقط نتأكد من z-index
    const menu = trigger.parentElement.querySelector('.dropdown__menu');
    if (menu) {
      menu.style.zIndex = '9999';
      menu.style.overflow = 'visible';
    }
  }

  // User Menu Sidebar - حل أنيق لمشكلة القائمة المحصورة
  initiateSallaUserMenu() {
    // إنشاء السايدبار فوراً
    setTimeout(() => {
      this.createUserMenuSidebar();
      this.attachUserMenuEvents();
    }, 1000);

    // معالج فوري للنقر على أي زر في salla-user-menu
    document.addEventListener('click', (e) => {
      if (e.target.closest('salla-user-menu')) {
        console.log('تم النقر على salla-user-menu');
        e.preventDefault();
        e.stopPropagation();
        this.openUserMenuSidebar();
      }
    });
  }

  createUserMenuSidebar() {
    // إنشاء السايدبار إذا لم يكن موجوداً
    if (document.querySelector('.user-menu-sidebar')) {
      console.log('السايدبار موجود بالفعل');
      return;
    }

    console.log('إنشاء السايدبار...');

    const sidebar = document.createElement('div');
    sidebar.className = 'user-menu-sidebar';
    sidebar.innerHTML = `
      <div class="sidebar-header">
        <div class="user-info">
          <div class="user-avatar" style="width: 50px; height: 50px; border-radius: 50%; background: #00d4ff; display: flex; align-items: center; justify-content: center; color: white; font-weight: bold;">U</div>
          <div class="user-details">
            <h3>المستخدم</h3>
            <p>مرحباً بك</p>
          </div>
        </div>
        <button class="close-btn" aria-label="إغلاق">
          ✕
        </button>
      </div>
      <div class="sidebar-menu">
        <a href="/customer/profile" class="menu-item">
          <i class="menu-icon">👤</i>
          <span class="menu-text">الملف الشخصي</span>
        </a>
        <a href="/customer/orders" class="menu-item">
          <i class="menu-icon">📋</i>
          <span class="menu-text">طلباتي</span>
        </a>
        <a href="/customer/wishlist" class="menu-item">
          <i class="menu-icon">❤️</i>
          <span class="menu-text">المفضلة</span>
        </a>
        <a href="/customer/logout" class="menu-item logout">
          <i class="menu-icon">🚪</i>
          <span class="menu-text">تسجيل الخروج</span>
        </a>
      </div>
    `;

    // إنشاء الخلفية
    const backdrop = document.createElement('div');
    backdrop.className = 'user-menu-backdrop';

    // إضافة العناصر للصفحة
    document.body.appendChild(backdrop);
    document.body.appendChild(sidebar);

    console.log('تم إنشاء السايدبار');

    // إضافة معالجات الأحداث
    const closeBtn = sidebar.querySelector('.close-btn');
    closeBtn.addEventListener('click', () => {
      console.log('إغلاق السايدبار');
      this.closeUserMenuSidebar();
    });

    backdrop.addEventListener('click', () => {
      console.log('النقر على الخلفية');
      this.closeUserMenuSidebar();
    });

    // إغلاق بمفتاح Escape
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && sidebar.classList.contains('open')) {
        this.closeUserMenuSidebar();
      }
    });
  }

  attachUserMenuEvents() {
    console.log('ربط أحداث قائمة المستخدم...');

    // البحث عن أزرار قائمة المستخدم وربطها بالسايدبار
    const checkForUserMenuButtons = () => {
      console.log('البحث عن أزرار قائمة المستخدم...');

      // البحث في أماكن مختلفة
      const possibleSelectors = [
        'salla-user-menu',
        'salla-user-menu button',
        'salla-user-menu .dropdown__trigger',
        'salla-user-menu [data-dropdown-trigger]',
        '.s-user-menu-trigger',
        '.user-menu-trigger'
      ];

      let buttonsFound = 0;

      possibleSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
          if (!element.hasAttribute('data-sidebar-attached')) {
            element.setAttribute('data-sidebar-attached', 'true');

            // إضافة معالج النقر
            element.addEventListener('click', (e) => {
              console.log(`تم النقر على زر قائمة المستخدم: ${selector}`);
              e.preventDefault();
              e.stopPropagation();
              this.openUserMenuSidebar();
            });

            buttonsFound++;
            console.log(`تم ربط الزر: ${selector}`);
          }
        });
      });

      console.log(`تم ربط ${buttonsFound} زر`);
      return buttonsFound;
    };

    // فحص فوري
    const initialButtons = checkForUserMenuButtons();

    // فحص دوري إذا لم نجد أزرار
    if (initialButtons === 0) {
      console.log('لم يتم العثور على أزرار، بدء الفحص الدوري...');
      const interval = setInterval(() => {
        const found = checkForUserMenuButtons();
        if (found > 0) {
          console.log('تم العثور على الأزرار، إيقاف الفحص الدوري');
          clearInterval(interval);
        }
      }, 1000);

      // إيقاف الفحص بعد 15 ثانية
      setTimeout(() => {
        clearInterval(interval);
        console.log('انتهى وقت البحث عن الأزرار');
      }, 15000);
    }
  }

  openUserMenuSidebar() {
    console.log('محاولة فتح السايدبار...');

    // إنشاء السايدبار إذا لم يكن موجوداً
    if (!document.querySelector('.user-menu-sidebar')) {
      this.createUserMenuSidebar();
    } else {
      // تحديث محتوى السايدبار بالبيانات الحديثة
      this.updateSidebarContent();
    }

    const sidebar = document.querySelector('.user-menu-sidebar');
    const backdrop = document.querySelector('.user-menu-backdrop');

    console.log('السايدبار:', sidebar);
    console.log('الخلفية:', backdrop);

    if (sidebar && backdrop) {
      sidebar.classList.add('open');
      backdrop.classList.add('open');
      document.body.style.overflow = 'hidden';
      console.log('تم فتح السايدبار');
    } else {
      console.log('لم يتم العثور على السايدبار أو الخلفية');
    }
  }

  updateSidebarContent() {
    console.log('تحديث محتوى السايدبار...');
    const sidebar = document.querySelector('.user-menu-sidebar');
    if (!sidebar) return;

    // تحديث معلومات المستخدم
    const userNameElement = sidebar.querySelector('.user-details h3');
    const userEmailElement = sidebar.querySelector('.user-details p');
    const userAvatarElement = sidebar.querySelector('.user-avatar');

    if (userNameElement) {
      userNameElement.textContent = this.getUserName();
    }
    if (userEmailElement) {
      userEmailElement.textContent = this.getUserEmail();
    }
    if (userAvatarElement) {
      const avatar = this.getUserAvatar();
      if (avatar.startsWith('data:')) {
        userAvatarElement.innerHTML = avatar.replace('data:image/svg+xml,', '').replace(/%3C/g, '<').replace(/%3E/g, '>').replace(/%20/g, ' ').replace(/%22/g, '"');
      } else {
        userAvatarElement.innerHTML = `<img src="${avatar}" alt="User Avatar" style="width: 100%; height: 100%; border-radius: 50%;">`;
      }
    }

    // تحديث عناصر القائمة
    const menuContainer = sidebar.querySelector('.sidebar-menu');
    if (menuContainer) {
      menuContainer.innerHTML = this.getUserMenuItems();
    }
  }

  closeUserMenuSidebar() {
    console.log('إغلاق السايدبار...');
    const sidebar = document.querySelector('.user-menu-sidebar');
    const backdrop = document.querySelector('.user-menu-backdrop');

    if (sidebar && backdrop) {
      sidebar.classList.remove('open');
      backdrop.classList.remove('open');
      document.body.style.overflow = '';
      console.log('تم إغلاق السايدبار');
    }
  }

  getUserAvatar() {
    console.log('البحث عن صورة المستخدم...');

    // البحث في أماكن مختلفة لصورة المستخدم
    const possibleSelectors = [
      'salla-user-menu img',
      '.user-avatar img',
      '.s-user-menu-avatar img',
      'salla-user-menu .avatar img',
      '[data-user-avatar]',
      '.customer-avatar img'
    ];

    for (const selector of possibleSelectors) {
      const avatar = document.querySelector(selector);
      if (avatar && avatar.src && !avatar.src.includes('placeholder')) {
        console.log(`تم العثور على صورة المستخدم: ${selector}`);
        return avatar.src;
      }
    }

    // البحث عن صورة في خلفية CSS
    const userMenuElement = document.querySelector('salla-user-menu');
    if (userMenuElement) {
      const computedStyle = window.getComputedStyle(userMenuElement);
      const backgroundImage = computedStyle.backgroundImage;
      if (backgroundImage && backgroundImage !== 'none') {
        const urlMatch = backgroundImage.match(/url\(['"]?([^'"]+)['"]?\)/);
        if (urlMatch && urlMatch[1]) {
          console.log('تم العثور على صورة المستخدم من CSS background');
          return urlMatch[1];
        }
      }
    }

    console.log('لم يتم العثور على صورة، إنشاء أفاتار بالأحرف الأولى');

    // إنشاء أفاتار افتراضي بالأحرف الأولى
    const userName = this.getUserName();
    const initials = userName.split(' ').map(n => n[0]).join('').toUpperCase().substring(0, 2);

    return `data:image/svg+xml,${encodeURIComponent(`
      <svg width="50" height="50" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" style="stop-color:#00d4ff;stop-opacity:1" />
            <stop offset="100%" style="stop-color:#8b5cf6;stop-opacity:1" />
          </linearGradient>
        </defs>
        <circle cx="25" cy="25" r="25" fill="url(#grad1)"/>
        <text x="25" y="32" text-anchor="middle" fill="white" font-size="16" font-weight="bold" font-family="Arial">${initials}</text>
      </svg>
    `)}`;
  }

  getUserName() {
    console.log('البحث عن اسم المستخدم...');

    // محاولة الحصول على اسم المستخدم من Salla
    if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
      const userName = salla.config.get('user.name');
      if (userName) {
        console.log(`اسم المستخدم من Salla: ${userName}`);
        return userName;
      }
    }

    // البحث في عناصر مختلفة
    const possibleSelectors = [
      '[data-user-name]',
      '.user-name',
      'salla-user-menu [data-name]',
      'salla-user-menu .name',
      '.s-user-menu-name',
      '.customer-name'
    ];

    for (const selector of possibleSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        const name = element.textContent.trim();
        console.log(`اسم المستخدم من ${selector}: ${name}`);
        return name;
      }
    }

    // محاولة استخراج من القائمة المنسدلة نفسها
    const userMenu = document.querySelector('salla-user-menu');
    if (userMenu) {
      const textContent = userMenu.textContent;
      // البحث عن نمط "مرحباً [الاسم]" أو "أهلاً [الاسم]"
      const greetingMatch = textContent.match(/(?:مرحباً|أهلاً|مرحبا)\s+([^\s]+)/);
      if (greetingMatch && greetingMatch[1]) {
        console.log(`اسم المستخدم من التحية: ${greetingMatch[1]}`);
        return greetingMatch[1];
      }
    }

    console.log('لم يتم العثور على اسم المستخدم، استخدام الافتراضي');
    return 'المستخدم';
  }

  getUserEmail() {
    console.log('البحث عن بريد المستخدم...');

    // محاولة الحصول على بريد المستخدم من Salla
    if (typeof salla !== 'undefined' && salla.config && salla.config.get) {
      const userEmail = salla.config.get('user.email');
      if (userEmail) {
        console.log(`بريد المستخدم من Salla: ${userEmail}`);
        return userEmail;
      }
    }

    // البحث في عناصر مختلفة
    const possibleSelectors = [
      '[data-user-email]',
      '.user-email',
      'salla-user-menu [data-email]',
      'salla-user-menu .email',
      '.s-user-menu-email',
      '.customer-email'
    ];

    for (const selector of possibleSelectors) {
      const element = document.querySelector(selector);
      if (element && element.textContent.trim()) {
        const email = element.textContent.trim();
        console.log(`بريد المستخدم من ${selector}: ${email}`);
        return email;
      }
    }

    console.log('لم يتم العثور على بريد المستخدم، استخدام الافتراضي');
    return 'مرحباً بك';
  }

  getUserMenuItems() {
    console.log('البحث عن عناصر القائمة الأصلية...');

    // البحث في أماكن مختلفة للقائمة الأصلية
    const possibleSelectors = [
      '.s-user-menu-dropdown-list',
      '.s-user-menu-dropdown',
      '.dropdown__menu',
      'salla-user-menu .dropdown__menu',
      'salla-user-menu ul',
      'salla-user-menu .menu-list'
    ];

    let originalMenu = null;
    for (const selector of possibleSelectors) {
      originalMenu = document.querySelector(selector);
      if (originalMenu) {
        console.log(`تم العثور على القائمة الأصلية: ${selector}`);
        break;
      }
    }

    if (originalMenu) {
      const items = originalMenu.querySelectorAll('a, li a, .menu-item');
      console.log(`تم العثور على ${items.length} عنصر في القائمة`);

      if (items.length > 0) {
        return Array.from(items).map(item => {
          // استخراج الأيقونة
          let iconClass = 'sicon-user';
          const iconElement = item.querySelector('i, .icon, [class*="icon"]');
          if (iconElement) {
            iconClass = iconElement.className || iconClass;
          }

          // استخراج النص
          let text = item.textContent.trim();
          if (!text && item.getAttribute('title')) {
            text = item.getAttribute('title');
          }

          // استخراج الرابط
          let href = item.href || item.getAttribute('href') || '#';

          // تحديد نوع العنصر
          const isLogout = text.includes('خروج') || text.includes('logout') ||
                          href.includes('logout') || text.includes('تسجيل الخروج');

          console.log(`عنصر القائمة: ${text} - ${href}`);

          return `
            <a href="${href}" class="menu-item ${isLogout ? 'logout' : ''}" ${isLogout ? 'onclick="return confirm(\'هل أنت متأكد من تسجيل الخروج؟\')"' : ''}>
              <i class="menu-icon ${iconClass}"></i>
              <span class="menu-text">${text}</span>
            </a>
          `;
        }).join('');
      }
    }

    console.log('لم يتم العثور على القائمة الأصلية، استخدام العناصر الافتراضية');

    // عناصر افتراضية مع روابط Salla الصحيحة
    const defaultItems = [
      { icon: 'sicon-user', text: 'الملف الشخصي', href: salla?.url?.get('customer.profile') || '/customer/profile' },
      { icon: 'sicon-receipt', text: 'طلباتي', href: salla?.url?.get('customer.orders') || '/customer/orders' },
      { icon: 'sicon-heart', text: 'المفضلة', href: salla?.url?.get('customer.wishlist') || '/customer/wishlist' },
      { icon: 'sicon-location', text: 'العناوين', href: salla?.url?.get('customer.addresses') || '/customer/addresses' },
      { icon: 'sicon-wallet', text: 'المحفظة', href: salla?.url?.get('customer.wallet') || '/customer/wallet' },
      { icon: 'sicon-logout', text: 'تسجيل الخروج', href: salla?.url?.get('customer.logout') || '/customer/logout', class: 'logout' }
    ];

    return defaultItems.map(item => `
      <a href="${item.href}" class="menu-item ${item.class || ''}" ${item.class === 'logout' ? 'onclick="return confirm(\'هل أنت متأكد من تسجيل الخروج؟\')"' : ''}>
        <i class="menu-icon ${item.icon}"></i>
        <span class="menu-text">${item.text}</span>
      </a>
    `).join('');
  }

  initiateModals() {
    this.onClick('[data-modal-trigger]', e => {
      let id = '#' + e.target.dataset.modalTrigger;
      this.removeClass(id, 'hidden');
      setTimeout(() => this.toggleModal(id, true)); //small amont of time to running toggle After adding hidden
    });
    salla.event.document.onClick("[data-close-modal]", e => this.toggleModal('#' + e.target.dataset.closeModal, false));
  }

  toggleModal(id, isOpen) {
    this.toggleClassIf(`${id} .s-salla-modal-overlay`, 'ease-out duration-300 opacity-100', 'opacity-0', () => isOpen)
      .toggleClassIf(`${id} .s-salla-modal-body`,
        'ease-out duration-300 opacity-100 translate-y-0 sm:scale-100', //add these classes
        'opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95', //remove these classes
        () => isOpen)
      .toggleElementClassIf(document.body, 'modal-is-open', 'modal-is-closed', () => isOpen);
    if (!isOpen) {
      setTimeout(() => this.addClass(id, 'hidden'), 350);
    }
  }

  initiateCollapse() {
    document.querySelectorAll('.btn--collapse')
      .forEach((trigger) => {
        const content = document.querySelector('#' + trigger.dataset.show);
        const state = { isOpen: false }

        const onOpen = () => anime({
          targets: content,
          duration: 225,
          height: content.scrollHeight,
          opacity: [0, 1],
          easing: 'easeOutQuart',
        });

        const onClose = () => anime({
          targets: content,
          duration: 225,
          height: 0,
          opacity: [1, 0],
          easing: 'easeOutQuart',
        })

        const toggleState = (isOpen) => {
          state.isOpen = !isOpen
          this.toggleElementClassIf([content, trigger], 'is-closed', 'is-opened', () => isOpen);
        }

        trigger.addEventListener('click', () => {
          const { isOpen } = state
          toggleState(isOpen)
          isOpen ? onClose() : onOpen();
        })
      });
  }


  /**
   * Workaround for seeking to simplify & clean, There are three ways to use this method:
   * 1- direct call: `this.anime('.my-selector')` - will use default values
   * 2- direct call with overriding defaults: `this.anime('.my-selector', {duration:3000})`
   * 3- return object to play it letter: `this.anime('.my-selector', false).duration(3000).play()` - will not play animation unless calling play method.
   * @param {string|HTMLElement} selector
   * @param {object|undefined|null|null} options - in case there is need to set attributes one by one set it `false`;
   * @return {Anime|*}
   */
  anime(selector, options = null) {
    let anime = new Anime(selector, options);
    return options === false ? anime : anime.play();
  }

  /**
   * These actions are responsible for pressing "add to cart" button,
   * they can be from any page, especially when mega-menu is enabled
   */
  initAddToCart() {
    salla.cart.event.onUpdated(summary => {
      document.querySelectorAll('[data-cart-total]').forEach(el => el.innerHTML = salla.money(summary.total));
      document.querySelectorAll('[data-cart-count]').forEach(el => el.innerText = salla.helpers.number(summary.count));
    });

    salla.cart.event.onItemAdded((response, prodId) => {
      app.element('salla-cart-summary').animateToCart(app.element(`#product-${prodId} img`));
    });
  }

  // Banner with Offer Component functionality
  initBannerWithOffer() {
    // Initialize all banner offer components
    document.querySelectorAll('[id^="offer-component-"]').forEach(component => {
      const uniqueId = component.id.replace('offer-component-', '');
      const days = parseInt(component.dataset.days) || 7;

      // Calculate end date
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + days);

      const updateCountdown = () => {
        const now = new Date().getTime();
        const distance = endDate.getTime() - now;

        if (distance < 0) {
          // Offer ended
          const timer = document.getElementById('countdown-timer-' + uniqueId);
          const badge = document.getElementById('offer-ended-badge-' + uniqueId);
          if (timer) timer.style.display = 'none';
          if (badge) badge.style.display = 'flex';
          return;
        }

        // Calculate remaining time
        const daysLeft = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        // Update display
        const elements = {
          days: document.getElementById('days-' + uniqueId),
          hours: document.getElementById('hours-' + uniqueId),
          minutes: document.getElementById('minutes-' + uniqueId),
          seconds: document.getElementById('seconds-' + uniqueId)
        };

        if (elements.days) elements.days.textContent = String(daysLeft).padStart(2, '0');
        if (elements.hours) elements.hours.textContent = String(hours).padStart(2, '0');
        if (elements.minutes) elements.minutes.textContent = String(minutes).padStart(2, '0');
        if (elements.seconds) elements.seconds.textContent = String(seconds).padStart(2, '0');
      };

      // Start countdown
      updateCountdown();
      setInterval(updateCountdown, 1000);
    });
  }
}

salla.onReady(() => (new App).loadTheApp());
