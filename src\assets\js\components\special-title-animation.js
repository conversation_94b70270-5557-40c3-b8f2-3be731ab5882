/**
 * Special Title Animation Component
 * Adds tracking-in-expand animation and gentle bouncing effects when component comes into view
 * Similar to lazy loading behavior
 */
(function() {
    'use strict';
    
    let isInitialized = false;
    const titleObservers = new Map();
    
    class SpecialTitleAnimator {
        constructor(titleComponent) {
            this.titleComponent = titleComponent;
            this.observer = null;
            this.hasAnimated = false;
            this.elements = {
                section: null,
                wrapper: null,
                title: null,
                decoration: null,
                decorationLines: [],
                decorationIcon: null,
                particles: null,
                particleElements: [],
                circuitDecoration: null,
                circuitLines: [],
                circuitDots: []
            };
        }
        
        init() {
            this.findElements();
            this.setupAnimation();
        }
        
        findElements() {
            this.elements.section = this.titleComponent;
            this.elements.wrapper = this.titleComponent.querySelector('.special-title-wrapper');
            this.elements.title = this.titleComponent.querySelector('.special-title-text');
            this.elements.decoration = this.titleComponent.querySelector('.special-title-decoration');
            this.elements.decorationLines = Array.from(this.titleComponent.querySelectorAll('.decoration-line'));
            this.elements.decorationIcon = this.titleComponent.querySelector('.decoration-icon');
            this.elements.particles = this.titleComponent.querySelector('.special-title-particles');
            this.elements.particleElements = Array.from(this.titleComponent.querySelectorAll('.particle'));
            this.elements.circuitDecoration = this.titleComponent.querySelector('.circuit-decoration');
            this.elements.circuitLines = Array.from(this.titleComponent.querySelectorAll('.circuit-line'));
            this.elements.circuitDots = Array.from(this.titleComponent.querySelectorAll('.circuit-dot'));
        }
        
        setupAnimation() {
            if (!('IntersectionObserver' in window)) {
                this.fallbackAnimation();
                return;
            }
            
            this.observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting && !this.hasAnimated) {
                        // Add slight delay for smoother effect
                        setTimeout(() => {
                            this.animateTitle();
                        }, 100);
                        this.hasAnimated = true;
                        this.observer.unobserve(entry.target);
                    }
                });
            }, {
                threshold: 0.15,
                rootMargin: '50px'
            });
            
            this.observer.observe(this.titleComponent);
        }
        
        animateTitle() {
            // Animate main section
            if (this.elements.section) {
                this.elements.section.classList.add('animate-in');
            }
            
            // Animate wrapper with delay
            if (this.elements.wrapper) {
                setTimeout(() => {
                    this.elements.wrapper.classList.add('animate-in');
                }, 200);
            }
            
            // Animate title with tracking-in-expand effect
            if (this.elements.title) {
                setTimeout(() => {
                    this.elements.title.classList.add('animate-in');
                }, 400);
            }
            
            // Animate decoration with delay
            if (this.elements.decoration) {
                setTimeout(() => {
                    this.elements.decoration.classList.add('animate-in');
                }, 800);
            }
            
            // Animate decoration lines with staggered delay
            this.elements.decorationLines.forEach((line, index) => {
                setTimeout(() => {
                    line.classList.add('animate-in');
                }, 1000 + (index * 200));
            });
            
            // Animate decoration icon
            if (this.elements.decorationIcon) {
                setTimeout(() => {
                    this.elements.decorationIcon.classList.add('animate-in');
                }, 1200);
            }
            
            // Animate particles container
            if (this.elements.particles) {
                setTimeout(() => {
                    this.elements.particles.classList.add('animate-in');
                }, 1400);
            }
            
            // Animate individual particles with staggered delay
            this.elements.particleElements.forEach((particle, index) => {
                setTimeout(() => {
                    particle.classList.add('animate-in');
                }, 1600 + (index * 100));
            });
            
            // Animate circuit decoration
            if (this.elements.circuitDecoration) {
                setTimeout(() => {
                    this.elements.circuitDecoration.classList.add('animate-in');
                }, 1800);
            }
            
            // Animate circuit lines with staggered delay
            this.elements.circuitLines.forEach((line, index) => {
                setTimeout(() => {
                    line.classList.add('animate-in');
                }, 2000 + (index * 300));
            });
            
            // Animate circuit dots with staggered delay
            this.elements.circuitDots.forEach((dot, index) => {
                setTimeout(() => {
                    dot.classList.add('animate-in');
                }, 2200 + (index * 400));
            });
        }
        
        fallbackAnimation() {
            // Immediate animation for browsers without IntersectionObserver
            if (!this.hasAnimated) {
                this.animateTitle();
                this.hasAnimated = true;
            }
        }
        
        destroy() {
            if (this.observer) {
                this.observer.disconnect();
                this.observer = null;
            }
            this.hasAnimated = false;
        }
    }
    
    function initTitle(titleComponent) {
        const titleId = titleComponent.id || `special-title-${Date.now()}-${Math.random()}`;
        
        if (titleObservers.has(titleId)) return;
        
        const animator = new SpecialTitleAnimator(titleComponent);
        titleObservers.set(titleId, animator);
        
        animator.init();
    }
    
    function init() {
        if (isInitialized) return;
        
        const titleComponents = document.querySelectorAll('.special-title-component');
        titleComponents.forEach(initTitle);
        
        // Watch for dynamically added title components
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const addedTitles = Array.from(mutation.addedNodes)
                        .filter(node => node.nodeType === 1 && node.classList?.contains('special-title-component'));
                    
                    addedTitles.forEach(initTitle);
                }
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
        
        isInitialized = true;
    }
    
    function cleanup() {
        titleObservers.forEach(animator => animator.destroy());
        titleObservers.clear();
        isInitialized = false;
    }
    
    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init, { once: true });
    } else {
        requestAnimationFrame(init);
    }
    
    // Expose for manual control
    window.specialTitleAnimator = {
        init,
        cleanup,
        initTitle: initTitle
    };
    
})();

// Enhanced title interactions
document.addEventListener('DOMContentLoaded', function() {
    const titleComponents = document.querySelectorAll('.special-title-component');
    
    titleComponents.forEach(component => {
        const titleText = component.querySelector('.special-title-text');
        
        if (titleText) {
            // Add enhanced hover effects
            let isAnimating = false;
            
            const handleMouseEnter = () => {
                if (isAnimating) return;
                isAnimating = true;
                
                requestAnimationFrame(() => {
                    titleText.style.transform = 'translateY(-5px) scale(1.05)';
                    titleText.style.textShadow = '0 0 30px rgba(29, 233, 182, 1), 0 0 60px rgba(29, 233, 182, 0.6)';
                    
                    isAnimating = false;
                });
            };
            
            const handleMouseLeave = () => {
                if (isAnimating) return;
                isAnimating = true;
                
                requestAnimationFrame(() => {
                    titleText.style.transform = '';
                    titleText.style.textShadow = '';
                    
                    isAnimating = false;
                });
            };
            
            titleText.addEventListener('mouseenter', handleMouseEnter, { passive: true });
            titleText.addEventListener('mouseleave', handleMouseLeave, { passive: true });
            
            // Add accessibility support
            titleText.setAttribute('role', 'heading');
            titleText.setAttribute('aria-level', '1');
            
            // Add keyboard support
            titleText.addEventListener('keydown', (e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleMouseEnter();
                    setTimeout(handleMouseLeave, 200);
                }
            });
        }
        
        // Enhanced particle interactions
        const particles = component.querySelectorAll('.particle');
        particles.forEach((particle, index) => {
            // Add random delay to particle animations for more natural effect
            const randomDelay = Math.random() * 2;
            particle.style.animationDelay = `${randomDelay}s`;
            
            // Add slight random positioning variation
            const randomX = (Math.random() - 0.5) * 10;
            const randomY = (Math.random() - 0.5) * 10;
            particle.style.transform = `translate(${randomX}px, ${randomY}px)`;
        });
    });
});
